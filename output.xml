<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.2.2 (Python 3.12.10 on win32)" generated="2025-06-06T15:22:25.327909" rpa="false" schemaversion="5">
<suite id="s1" name="ATMDetails" source="c:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot">
<test id="s1-t1" name="Unit Test" line="857">
<kw name="The user counts number of ATM rows on Database">
<kw name="Get VMS Gasper Details" owner="DatabaseConnector">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-06T15:22:27.754420" level="INFO">${db_type} = 'MSSQL'</msg>
<var>${db_type}</var>
<arg>'MSSQL'</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-06T15:22:27.753909" elapsed="0.006535"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-06T15:22:27.761443" level="INFO">${row_count_query} = SELECT COUNT(*) AS ROW_NUMBERS FROM [VMS_UAT].[core].[gasper_details]</msg>
<var>${row_count_query}</var>
<arg>${SQL_GET_VMS_GASPER_DETAILS}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-06T15:22:27.760444" elapsed="0.000999"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-06T15:22:27.761443" level="INFO">${DB_TYPE} = MSSQL</msg>
<var>${DB_TYPE}</var>
<arg>MSSQL</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-06T15:22:27.761443" elapsed="0.000999"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-06T15:22:27.782105" level="INFO">Property value fetched is:  XZAPBCC1SQL1004</msg>
<msg time="2025-06-06T15:22:27.783036" level="INFO">${DB_HOST} = XZAPBCC1SQL1004</msg>
<var>${DB_HOST}</var>
<arg>MS_DB_HOST</arg>
<status status="PASS" start="2025-06-06T15:22:27.762442" elapsed="0.020594"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-06T15:22:27.797102" level="INFO">Property value fetched is:  VMS_UAT</msg>
<msg time="2025-06-06T15:22:27.798105" level="INFO">${DB_NAME} = VMS_UAT</msg>
<var>${DB_NAME}</var>
<arg>MS_DB_SCHEMA</arg>
<status status="PASS" start="2025-06-06T15:22:27.783036" elapsed="0.015069"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-06T15:22:27.812630" level="INFO">Property value fetched is:  apl</msg>
<msg time="2025-06-06T15:22:27.812630" level="INFO">${DB_USER} = apl</msg>
<var>${DB_USER}</var>
<arg>MS_DB_User</arg>
<status status="PASS" start="2025-06-06T15:22:27.799105" elapsed="0.013525"/>
</kw>
<kw name="Read Config Property" owner="CommonUtils">
<msg time="2025-06-06T15:22:27.822159" level="INFO">Property value fetched is:  Pa$$w0rd</msg>
<msg time="2025-06-06T15:22:27.822159" level="INFO">${DB_PASSWORD} = Pa$$w0rd</msg>
<var>${DB_PASSWORD}</var>
<arg>MS_DB_PWD</arg>
<status status="PASS" start="2025-06-06T15:22:27.812630" elapsed="0.009529"/>
</kw>
<kw name="Execute Select Query" owner="DatabaseConnector">
<kw name="Execute Select Statement" owner="Database_Library">
<msg time="2025-06-06T15:22:28.035816" level="INFO">MSSQL XZAPBCC1SQL1004 VMS_UAT apl Pa$$w0rd
Connection established, executing query.</msg>
<msg time="2025-06-06T15:22:28.035816" level="INFO">${results} = [{'ROW_NUMBERS': 5808}]</msg>
<var>${results}</var>
<arg>${db_type}</arg>
<arg>${host}</arg>
<arg>${database}</arg>
<arg>${username}</arg>
<arg>${password}</arg>
<arg>${query}</arg>
<doc>Executes a SELECT query on either MySQL or MSSQL and returns the results as a dictionary.
Raises an error if the query fails.</doc>
<status status="PASS" start="2025-06-06T15:22:27.823166" elapsed="0.212650"/>
</kw>
<kw name="Return From Keyword" owner="BuiltIn">
<msg time="2025-06-06T15:22:28.036802" level="INFO">Returning from the enclosing user keyword.</msg>
<arg>${results}</arg>
<doc>Returns from the enclosing user keyword.</doc>
<status status="PASS" start="2025-06-06T15:22:28.036802" elapsed="0.000000"/>
</kw>
<msg time="2025-06-06T15:22:28.036802" level="INFO">${results} = [{'ROW_NUMBERS': 5808}]</msg>
<var>${results}</var>
<arg>${DB_TYPE}</arg>
<arg>${DB_HOST}</arg>
<arg>${DB_NAME}</arg>
<arg>${DB_USER}</arg>
<arg>${DB_PASSWORD}</arg>
<arg>${row_count_query}</arg>
<doc>Execute a database query against the VMS database and return the results
This keyword handles the connection to the VMS database,
executes the query, and returns the results.</doc>
<status status="PASS" start="2025-06-06T15:22:27.823166" elapsed="0.213636"/>
</kw>
<return>
<value>${results}</value>
<status status="PASS" start="2025-06-06T15:22:28.037801" elapsed="0.000000"/>
</return>
<msg time="2025-06-06T15:22:28.037801" level="INFO">${db_row_count} = [{'ROW_NUMBERS': 5808}]</msg>
<var>${db_row_count}</var>
<status status="PASS" start="2025-06-06T15:22:27.752910" elapsed="0.285889"/>
</kw>
<kw name="Get From List" owner="Collections">
<msg time="2025-06-06T15:22:28.039806" level="INFO">${db_first_row} = {'ROW_NUMBERS': 5808}</msg>
<var>${db_first_row}</var>
<arg>${db_row_count}</arg>
<arg>0</arg>
<doc>Returns the value specified with an ``index`` from ``list``.</doc>
<status status="PASS" start="2025-06-06T15:22:28.038799" elapsed="0.001007"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-06-06T15:22:28.040805" level="INFO">${backend_row_count} = 5808</msg>
<var>${backend_row_count}</var>
<arg>${db_first_row}</arg>
<arg>ROW_NUMBERS</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-06-06T15:22:28.039806" elapsed="0.000999"/>
</kw>
<kw name="Set Global Variable" owner="BuiltIn">
<msg time="2025-06-06T15:22:28.040805" level="INFO">${Global_backend_row_count} = 5808</msg>
<arg>${Global_backend_row_count}</arg>
<arg>${backend_row_count}</arg>
<doc>Makes a variable available globally in all tests and suites.</doc>
<status status="PASS" start="2025-06-06T15:22:28.040805" elapsed="0.000000"/>
</kw>
<kw name="Log To Console" owner="BuiltIn">
<arg>--------------------------Backend Row Count: ${backend_row_count}</arg>
<doc>Logs the given message to the console.</doc>
<status status="PASS" start="2025-06-06T15:22:28.041801" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-06T15:22:27.751910" elapsed="0.290892"/>
</kw>
<status status="PASS" start="2025-06-06T15:22:27.750911" elapsed="0.292892"/>
</test>
<doc>VMS Dashboard Validation</doc>
<status status="PASS" start="2025-06-06T15:22:25.346889" elapsed="2.699205"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="ATMDetails" id="s1" pass="1" fail="0" skip="0">ATMDetails</stat>
</suite>
</statistics>
<errors>
<msg time="2025-06-06T15:22:25.194018" level="WARN">Error in file 'c:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 738: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-06-06T15:22:25.196968" level="WARN">Error in file 'c:\Alternative\Alternative_Physical_Channels_QA\vms\keywords\VMSPage\ATMDetails.robot' on line 821: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
</errors>
</robot>
